import fs from 'fs';
import path from 'path';

import axios from 'axios';

const SWAGGER_URL = 'http://localhost:3030/swagger.json';
const OUTPUT_DIR = path.resolve('docs/api');

async function fetchSwagger() {
  try {
    const response = await axios.get(SWAGGER_URL);
    return response.data;
  } catch (error) {
    console.error('Error fetching Swagger JSON:', error.message);
    process.exit(1);
  }
}

function saveEndpointFiles(swaggerData) {
  if (!fs.existsSync(OUTPUT_DIR)) {
    fs.mkdirSync(OUTPUT_DIR, { recursive: true });
  }

  const paths = swaggerData.paths;
  for (const [endpoint, methods] of Object.entries(paths)) {
    const fileName = endpoint.replace(/\//g, '_').replace(/[^a-zA-Z0-9_]/g, '') + '.json';
    const filePath = path.join(OUTPUT_DIR, fileName);

    fs.writeFileSync(filePath, JSON.stringify({ endpoint, methods }, null, 2));
    console.log(`Saved: ${filePath}`);
  }
}

function generateIndexFile(swaggerData) {
  const indexFilePath = path.join(OUTPUT_DIR, 'index.json');
  const summary = Object.keys(swaggerData.paths).map(endpoint => {
    const fileName = endpoint.replace(/\//g, '_').replace(/[^a-zA-Z0-9_]/g, '') + '.json';
    return { endpoint, fileName };
  });

  fs.writeFileSync(indexFilePath, JSON.stringify(summary, null, 2));
  console.log(`Index file created: ${indexFilePath}`);
}

async function main() {
  console.log('Fetching Swagger JSON...');
  const swaggerData = await fetchSwagger();

  console.log('Splitting and saving endpoints...');
  saveEndpointFiles(swaggerData);

  console.log('Generating index file...');
  generateIndexFile(swaggerData);

  console.log('Done!');
}

main();
