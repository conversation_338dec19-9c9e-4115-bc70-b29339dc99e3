<script setup lang="ts">
import { ref } from 'vue'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Loader2, Facebook } from 'lucide-vue-next' // Globe removed, Facebook kept
import { useAuthStore } from '@/stores/auth'
import { useRouter, useRoute } from 'vue-router' // Added

const authStore = useAuthStore()
const router = useRouter() // Added
const route = useRoute() // Added
const email = ref('')
const password = ref('') // Added password field
const isLoading = ref(false)
const error = ref<string | null>(null)

async function onSubmit(event: Event) {
  event.preventDefault()
  isLoading.value = true
  error.value = null
  try {
    await authStore.login({ strategy: 'local', email: email.value, password: password.value })
    // MODIFIED: Handle redirection after successful login
    const redirectPath = route.query.redirect
    if (redirectPath) {
      router.push(redirectPath as string)
    } else {
      router.push({ name: 'home' }) // Default to home page
    }
  } catch (err) {
    if (err instanceof Error) {
      error.value = err.message || 'Login failed. Please check your credentials.'
    } else {
      error.value = 'An unknown error occurred during login.'
    }
  } finally {
    isLoading.value = false
  }
}
</script>

<template>
  <div :class="cn('grid gap-6', $attrs.class ?? '')">
    <form @submit="onSubmit">
      <div class="grid gap-4">
        <div class="grid gap-2">
          <Label for="email">
            Email
          </Label>
          <Input
            id="email"
            v-model="email"
            placeholder="<EMAIL>"
            type="email"
            auto-capitalize="none"
            auto-complete="email"
            auto-correct="off"
            :disabled="isLoading"
            required
          />
        </div>
        <div class="grid gap-2">
          <Label for="password">
            Password
          </Label>
          <Input
            id="password"
            v-model="password"
            type="password"
            auto-complete="current-password"
            :disabled="isLoading"
            required
          />
        </div>
        <Button :disabled="isLoading" class="w-full">
          <Loader2 v-if="isLoading" class="mr-2 h-4 w-4 animate-spin" />
          Sign In
        </Button>
        <p v-if="error" class="text-sm text-red-600 text-center">
          {{ error }}
        </p>
      </div>
    </form>
    <div class="relative">
      <div class="absolute inset-0 flex items-center">
        <span class="w-full border-t" />
      </div>
      <div class="relative flex justify-center text-xs uppercase">
        <span class="bg-background px-2 text-muted-foreground">
          Or continue with
        </span>
      </div>
    </div>
    <Button variant="outline" type="button" :disabled="isLoading" class="w-full mb-2">
      <Loader2 v-if="isLoading" class="mr-2 h-4 w-4 animate-spin" />
      <svg v-else class="mr-2 h-4 w-4" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M17.64 9.20455C17.64 8.56636 17.5827 7.95273 17.4764 7.36364H9V10.845H13.8436C13.635 11.9705 13.0009 12.9232 12.0477 13.5614V15.8195H14.9564C16.6582 14.2527 17.64 11.9468 17.64 9.20455Z" fill="#4285F4"/>
        <path d="M9 18C11.43 18 13.4673 17.1941 14.9564 15.8195L12.0477 13.5614C11.2418 14.0918 10.2109 14.4205 9 14.4205C6.65591 14.4205 4.67182 12.8373 3.96409 10.71H0.957275V13.0418C2.43818 15.9832 5.48182 18 9 18Z" fill="#34A853"/>
        <path d="M3.96409 10.71C3.78409 10.1718 3.68182 9.59318 3.68182 9C3.68182 8.40682 3.78409 7.82818 3.96409 7.29V4.95818H0.957275C0.347727 6.17318 0 7.54773 0 9C0 10.4523 0.347727 11.8268 0.957275 13.0418L3.96409 10.71Z" fill="#FBBC05"/>
        <path d="M9 3.57955C10.3214 3.57955 11.5077 4.03364 12.4405 4.92545L15.0218 2.34545C13.4632 0.891818 11.4259 0 9 0C5.48182 0 2.43818 2.01682 0.957275 4.95818L3.96409 7.29C4.67182 5.16273 6.65591 3.57955 9 3.57955Z" fill="#EA4335"/>
      </svg>
      Google (Not Implemented)
    </Button>
    <Button variant="outline" type="button" :disabled="isLoading" class="w-full">
      <Loader2 v-if="isLoading" class="mr-2 h-4 w-4 animate-spin" />
      <Facebook v-else class="mr-2 h-4 w-4" />
      Facebook (Not Implemented)
    </Button>
  </div>
</template>
