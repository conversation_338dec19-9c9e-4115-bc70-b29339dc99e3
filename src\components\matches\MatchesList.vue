<script setup lang="ts">
import type { Match } from '@/api/feathers-client'
import MatchItem from './MatchItem.vue'
import { useMatchesStore } from '@/stores/matches'
import { storeToRefs } from 'pinia' // Import storeToRefs
import { computed } from 'vue'

// Define and destructure props
const { items, hoveredMatches = [] } = defineProps<{
  items: Match[]
  hoveredMatches?: Match[]
}>()

const matchesStore = useMatchesStore()
const { currentMatch } = storeToRefs(matchesStore) // Get currentMatch from store

const handleSelectMatch = (id: number) => {
  matchesStore.selectMatch(id)
}

// Create enhanced items with highlighting information
const enhancedItems = computed(() => {
  const hoveredIds = new Set(hoveredMatches.map(m => m.id))
  return items.map(item => ({
    ...item,
    isHighlighted: hoveredIds.has(item.id)
  }))
})
</script>

<template>
  <div class="flex flex-col gap-2 p-2 pt-0">
    <div v-if="!items || items.length === 0" class="p-8 text-center text-muted-foreground">
      No matches found
    </div>
    <div v-else class="space-y-2">
      <MatchItem
        v-for="item in enhancedItems"
        :key="item.id"
        :match="item"
        :is-selected="currentMatch?.id === item.id"
        @select="handleSelectMatch(item.id)"
      />
    </div>
  </div>
</template>
