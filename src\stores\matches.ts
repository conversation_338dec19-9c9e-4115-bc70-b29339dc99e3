import { defineStore } from 'pinia'
import { computed, ref, watch } from 'vue' // Import watch

import type { Params } from '@feathersjs/feathers'

import type { Match, MatchData, MatchPatch, MatchRegistration, MatchRegistrationData, MatchRegistrationPatch } from '../api/feathers-client' // Import types
import { api } from '../api/feathers-client' // Import api client

export const useMatchesStore = defineStore('matches', () => {
  // Use the typed service from the api client
  const matchesService = api.matches
  const matchRegistrationsService = api.matchRegistrations // Add service for registrations

  const matches = ref<Match[]>([])
  const currentMatch = ref<Match | null>(null)
  const hoveredMatchId = ref<number | null>(null); // New state for hovered match
  const isLoading = ref(false)
  const error = ref<Error | null>(null)

  // Search query for filtering matches
  const searchQuery = ref('')

  // State for Match Registrations
  const matchRegistrations = ref<MatchRegistration[]>([])
  const isLoadingRegistrations = ref(false)
  const registrationError = ref<Error | null>(null)

  const getMatchById = computed(() => (id: number) => {
    return matches.value.find(match => match.id === id)
  })

  // Filtered matches based on search query
  const filteredMatches = computed(() => {
    const query = searchQuery.value.trim().toLowerCase()
    if (!query) {
      return matches.value
    }
    return matches.value.filter(match =>
      match.name.toLowerCase().includes(query) ||
      match.city?.toLowerCase().includes(query) ||
      match.country?.toLowerCase().includes(query)
    )
  })

  // Watch for changes in filteredMatches to clear currentMatch if it's no longer in the list
  watch(filteredMatches, (newFilteredMatches) => {
    if (currentMatch.value && !newFilteredMatches.find(match => match.id === currentMatch.value!.id)) {
      clearSelectedMatch()
    }
  })

  async function findMatches(params?: Params) {
    isLoading.value = true
    error.value = null
    try {
      // Feathers `find` returns a paginated result
      const result = await matchesService.find(params)
      if (Array.isArray(result.data)) {
        matches.value = result.data
      } else {
        // Handle cases where result.data might not be an array (e.g. if not paginated)
        // This depends on your Feathers service setup
        matches.value = result as unknown as Match[] // Adjust if your service returns non-paginated directly
      }
    } catch (err) {
      if (err instanceof Error) {
        error.value = err
      } else {
        error.value = new Error('Failed to fetch matches.')
      }
      matches.value = []
    } finally {
      isLoading.value = false
    }
  }

  async function getMatch(id: number) {
    isLoading.value = true
    error.value = null
    try {
      const result = await matchesService.get(id)
      currentMatch.value = result
      // Optionally update the matches array if the fetched match is newer
      const index = matches.value.findIndex(m => m.id === id)
      if (index !== -1) {
        matches.value[index] = result
      } else {
        matches.value.push(result) // Or handle as per your app's logic
      }
      return result
    } catch (err) {
      if (err instanceof Error) {
        error.value = err
      } else {
        error.value = new Error(`Failed to fetch match with id ${id}.`)
      }
      currentMatch.value = null
      throw err
    } finally {
      isLoading.value = false
    }
  }

  async function createMatch(data: MatchData) { // Use MatchData for creation
    isLoading.value = true
    error.value = null
    try {
      const newMatch = await matchesService.create(data)
      matches.value.push(newMatch)
      return newMatch
    } catch (err) {
      if (err instanceof Error) {
        error.value = err
      } else {
        error.value = new Error('Failed to create match.')
      }
      throw err
    } finally {
      isLoading.value = false
    }
  }

  async function patchMatch(id: number, data: MatchPatch) { // Use MatchPatch for updates
    isLoading.value = true
    error.value = null
    try {
      const updatedMatch = await matchesService.patch(id, data)
      const index = matches.value.findIndex(m => m.id === id)
      if (index !== -1) {
        matches.value[index] = updatedMatch
      }
      if (currentMatch.value?.id === id) {
        currentMatch.value = updatedMatch
      }
      return updatedMatch
    } catch (err) {
      if (err instanceof Error) {
        error.value = err
      } else {
        error.value = new Error(`Failed to update match with id ${id}.`)
      }
      throw err
    } finally {
      isLoading.value = false
    }
  }

  async function removeMatch(id: number) {
    isLoading.value = true
    error.value = null
    try {
      const removedMatch = await matchesService.remove(id)
      matches.value = matches.value.filter(m => m.id !== id)
      if (currentMatch.value?.id === id) {
        currentMatch.value = null
      }
      return removedMatch
    } catch (err) {
      if (err instanceof Error) {
        error.value = err
      } else {
        error.value = new Error(`Failed to remove match with id ${id}.`)
      }
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // Real-time event listeners
  // Ensure these are cleaned up, e.g., in onUnmounted if store is used in a component scope
  // or manage them globally if the store is a singleton.
  matchesService.on('created', (match: Match) => {
    matches.value.push(match)
  })

  matchesService.on('updated', (match: Match) => {
    const index = matches.value.findIndex(m => m.id === match.id)
    if (index !== -1) {
      matches.value[index] = match
    }
    if (currentMatch.value?.id === match.id) {
      currentMatch.value = match
    }
  })

  matchesService.on('patched', (match: Match) => {
    const index = matches.value.findIndex(m => m.id === match.id)
    if (index !== -1) {
      matches.value[index] = match
    }
    if (currentMatch.value?.id === match.id) {
      currentMatch.value = match
    }
  })

  matchesService.on('removed', (match: Match) => {
    matches.value = matches.value.filter(m => m.id !== match.id)
    if (currentMatch.value?.id === match.id) {
      currentMatch.value = null
    }
  })

  // Action to set the current match
  function selectMatch(matchId: number) {
    if (currentMatch.value && currentMatch.value.id === matchId) {
      currentMatch.value = null; // Unselect if the same match is clicked again
    } else {
      const foundMatch = matches.value.find(m => m.id === matchId);
      if (foundMatch) {
        currentMatch.value = foundMatch;
      } else {
        // Optionally, fetch the match if not found in the current list
        // console.warn(`Match with id ${matchId} not found in local list. Consider fetching.`);
        // getMatch(matchId); // This would make selectMatch async
        currentMatch.value = null; // Or set to null if not found
      }
    }
  }

  function clearSelectedMatch() {
    currentMatch.value = null
  }

  // Action to update search query
  function setSearchQuery(query: string) {
    searchQuery.value = query
  }

  // Action to set hovered match ID
  function setHoveredMatchId(matchId: number | null) {
    hoveredMatchId.value = matchId;
  }

  // --- Match Registration Actions ---

  async function findMatchRegistrations(params?: Params) {
    isLoadingRegistrations.value = true
    registrationError.value = null
    try {
      const result = await matchRegistrationsService.find(params)
      if (Array.isArray(result.data)) {
        matchRegistrations.value = result.data
      } else {
        matchRegistrations.value = result as unknown as MatchRegistration[]
      }
    } catch (err) {
      if (err instanceof Error) {
        registrationError.value = err
      } else {
        registrationError.value = new Error('Failed to fetch match registrations.')
      }
      matchRegistrations.value = []
    } finally {
      isLoadingRegistrations.value = false
    }
  }

  async function createMatchRegistration(data: MatchRegistrationData) {
    isLoadingRegistrations.value = true
    registrationError.value = null
    try {
      const newRegistration = await matchRegistrationsService.create(data)
      matchRegistrations.value.push(newRegistration)
      return newRegistration
    } catch (err) {
      if (err instanceof Error) {
        registrationError.value = err
      } else {
        registrationError.value = new Error('Failed to create match registration.')
      }
      throw err
    } finally {
      isLoadingRegistrations.value = false
    }
  }

  async function patchMatchRegistration(registrationId: number, data: MatchRegistrationPatch) {
    isLoadingRegistrations.value = true
    registrationError.value = null
    try {
      const updatedRegistration = await matchRegistrationsService.patch(registrationId, data)
      const index = matchRegistrations.value.findIndex(r => r.id === registrationId)
      if (index !== -1) {
        matchRegistrations.value[index] = updatedRegistration
      }
      return updatedRegistration
    } catch (err) {
      if (err instanceof Error) {
        registrationError.value = err
      } else {
        registrationError.value = new Error(`Failed to update match registration with id ${registrationId}.`)
      }
      throw err
    } finally {
      isLoadingRegistrations.value = false
    }
  }

  async function removeMatchRegistration(registrationId: number) {
    isLoadingRegistrations.value = true
    registrationError.value = null
    try {
      const removedRegistration = await matchRegistrationsService.remove(registrationId)
      matchRegistrations.value = matchRegistrations.value.filter(r => r.id !== registrationId)
      return removedRegistration
    } catch (err) {
      if (err instanceof Error) {
        registrationError.value = err
      } else {
        registrationError.value = new Error(`Failed to remove match registration with id ${registrationId}.`)
      }
      throw err
    } finally {
      isLoadingRegistrations.value = false
    }
  }

  // Real-time event listeners for Match Registrations
  matchRegistrationsService.on('created', (registration: MatchRegistration) => {
    matchRegistrations.value.push(registration)
  })

  matchRegistrationsService.on('updated', (registration: MatchRegistration) => {
    const index = matchRegistrations.value.findIndex(r => r.id === registration.id)
    if (index !== -1) {
      matchRegistrations.value[index] = registration
    }
  })

  matchRegistrationsService.on('patched', (registration: MatchRegistration) => {
    const index = matchRegistrations.value.findIndex(r => r.id === registration.id)
    if (index !== -1) {
      matchRegistrations.value[index] = registration
    }
  })

  matchRegistrationsService.on('removed', (registration: MatchRegistration) => {
    matchRegistrations.value = matchRegistrations.value.filter(r => r.id !== registration.id)
  })

  return {
    matches,
    currentMatch,
    hoveredMatchId,
    isLoading,
    error,
    getMatchById,
    findMatches,
    getMatch,
    createMatch,
    patchMatch,
    removeMatch,
    selectMatch,
    clearSelectedMatch,
    // Expose search query and filtered matches
    searchQuery,
    setSearchQuery,
    filteredMatches,
    // Hovered match
    setHoveredMatchId,
    // Match Registrations
    matchRegistrations,
    isLoadingRegistrations,
    registrationError,
    findMatchRegistrations,
    createMatchRegistration,
    patchMatchRegistration,
    removeMatchRegistration,
  }
})
