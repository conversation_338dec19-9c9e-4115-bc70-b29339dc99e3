<script setup lang="ts">
import UserAuthForm from '@/components/common/UserAuthForm.vue' // Adjusted path

</script>

<template>
  <div class="container relative min-h-screen flex-col items-center justify-center grid lg:max-w-none lg:grid-cols-2 lg:px-0">

    <div
      class="relative hidden h-full flex-col bg-muted p-10 text-white dark:border-r lg:flex"
      style="background-image: url('/archer.jpg'); background-size: cover; background-position: center;"
    >
      <div class="absolute inset-0 bg-zinc-900 opacity-50" /> <!-- Added opacity to make text more readable -->
      <div class="relative z-20 flex items-center text-lg font-medium">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          class="mr-2 h-6 w-6"
        >
          <circle cx="12" cy="12" r="10"></circle>
          <circle cx="12" cy="12" r="6"></circle>
          <circle cx="12" cy="12" r="2"></circle>
        </svg>
        Archery Points
      </div>
      <div class="relative z-20 mt-auto">
        <blockquote class="space-y-2">
          <p class="text-lg">
            &ldquo;Track your scores, climb the ranks, and become a champion.&rdquo;
          </p>
          <footer class="text-sm">
            The Archery Points Team
          </footer>
        </blockquote>
      </div>
    </div>
    <div class="lg:p-8 flex items-center justify-center">
      <div class="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
        <div class="flex flex-col space-y-2 text-center">
          <h1 class="text-2xl font-semibold tracking-tight">
            Sign In
          </h1>
          <p class="text-sm text-muted-foreground">
            Enter your email and password to access your account.
          </p>
        </div>
        <UserAuthForm />
        <p class="px-8 text-center text-sm text-muted-foreground">
          By clicking continue, you agree to our
          <router-link
            to="/terms"
            class="underline underline-offset-4 hover:text-primary"
          >
            Terms of Service
          </router-link>
          and
          <router-link
            to="/privacy"
            class="underline underline-offset-4 hover:text-primary"
          >
            Privacy Policy
          </router-link>
          .
        </p>
      </div>
    </div>
  </div>
</template>
