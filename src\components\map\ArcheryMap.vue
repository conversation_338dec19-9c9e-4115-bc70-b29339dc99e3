<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, defineEmits } from 'vue'
import L, { type MarkerOptions, type PointExpression } from 'leaflet' // Import MarkerOptions and PointExpression
import 'leaflet/dist/leaflet.css'
import type { Match } from '@/api/feathers-client'
import { useMatchesStore } from '@/stores/matches';
import { storeToRefs } from 'pinia';

// Define custom marker options interface to include matchId
interface CustomMarkerOptions extends MarkerOptions {
  matchId?: number;
}

const props = defineProps<{
  latitude?: number | null;
  longitude?: number | null;
  matchesToDisplay?: Match[] | null;
  hoveredMatches?: Match[];
}>();

const emit = defineEmits<{
  (e: 'select-match', matchId: string): void;
}>();

const mapContainer = ref<HTMLElement | null>(null)
let map: L.Map | null = null
let primaryMatchMarker: <PERSON><PERSON> | null = null;
// Correctly type displayedMatchMarkers with the custom options
let displayedMatchMarkers: <PERSON><PERSON>Marker<CustomMarkerOptions>[] = [];

const matchesStore = useMatchesStore();
const { hoveredMatchId } = storeToRefs(matchesStore);

const svgPinPath = "M168.3 499.2C116.1 435 0 279.4 0 192C0 85.96 85.96 0 192 0C298 0 384 85.96 384 192C384 279.4 267 435 215.7 499.2C203.4 514.5 180.6 514.5 168.3 499.2H168.3zM192 256C227.3 256 256 227.3 256 192C256 156.7 227.3 128 192 128C156.7 128 128 156.7 128 192C128 227.3 156.7 256 192 256z";

const createColoredPinHtml = (color: string): string => `
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512" fill="${color}" width="100%" height="100%" style="display: block;">
    <path d="${svgPinPath}" stroke="#FFFFFF" stroke-width="10"/>
  </svg>
`;

const largeIconSize: PointExpression = [25, 41];
const largeIconAnchor: PointExpression = [12.5, 41];
const largePopupAnchor: PointExpression = [1, -34];

const smallIconSize: PointExpression = [15, 24];
const smallIconAnchor: PointExpression = [7.5, 24];
const smallPopupAnchor: PointExpression = [1, -20];

const defaultCenter: L.LatLngTuple = [52.0693, 19.4803];
const defaultZoom = 5;

const hoveredMatchIcon = L.divIcon({
  html: createColoredPinHtml('#FFC107'),
  iconSize: smallIconSize,
  iconAnchor: smallIconAnchor,
  popupAnchor: smallPopupAnchor,
  className: 'leaflet-svg-icon'
});

const selectedMatchIcon = L.divIcon({
  html: createColoredPinHtml('#294283'),
  iconSize: smallIconSize,
  iconAnchor: smallIconAnchor,
  popupAnchor: smallIconAnchor,
  className: 'leaflet-svg-icon'
});

const displayedMatchIcon = L.divIcon({
  html: createColoredPinHtml('#294283'),
  iconSize: smallIconSize,
  iconAnchor: smallIconAnchor,
  popupAnchor: smallPopupAnchor,
  className: 'leaflet-svg-icon'
});

const clearDisplayedMatchMarkers = () => {
  if (map) {
    displayedMatchMarkers.forEach(marker => map!.removeLayer(marker));
  }
  displayedMatchMarkers = [];
};

const displayFilteredMatches = (matches?: Match[] | null) => {
  if (!map) return;

  if (!matches || matches.length === 0) {
    map.setView(defaultCenter, defaultZoom);
    return;
  }

  const bounds = L.latLngBounds([]);
  matches.forEach(match => {
    if (match.latitude != null && match.longitude != null && match.id != null) {
      const location: L.LatLngTuple = [match.latitude, match.longitude];

      const popupContent = document.createElement('div');
      popupContent.className = 'flex items-center justify-between min-w-[150px]';
      const infoDiv = document.createElement('div');
      infoDiv.className = 'mr-2';
      const title = document.createElement('b');
      title.innerText = match.name;
      infoDiv.appendChild(title);
      const city = document.createElement('span');
      city.innerHTML = `<br>${match.city || 'N/A'}`;
      infoDiv.appendChild(city);
      popupContent.appendChild(infoDiv);

      const selectButton = document.createElement('button');
      selectButton.className = 'p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-700 flex-shrink-0';
      selectButton.title = 'Select Match';
      selectButton.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"/></svg>`;
      selectButton.onclick = (e) => {
        e.stopPropagation();
        if (match.id != null) emit('select-match', String(match.id));
      };
      popupContent.appendChild(selectButton);

      const markerOptions: CustomMarkerOptions = {
        icon: displayedMatchIcon,
        matchId: match.id
      };

      const marker = L.marker(location, markerOptions)
        .addTo(map!)
        .bindPopup(popupContent);
      displayedMatchMarkers.push(marker);
      bounds.extend(location);
    }
  });

  if (bounds.isValid()) {
    map.fitBounds(bounds, { padding: [30, 30], maxZoom: 15 });
  } else {
    map.setView(defaultCenter, defaultZoom);
  }
  // After displaying, ensure hover state is correct.
  updateHoveredMarker();
};

const updateMapForSelectedMatch = (lat?: number | null, lng?: number | null) => {
  if (!map) return;
  clearDisplayedMatchMarkers();

  if (primaryMatchMarker) {
    map.removeLayer(primaryMatchMarker);
    primaryMatchMarker = null;
  }

  if (lat != null && lng != null) {
    const matchLocation: L.LatLngTuple = [lat, lng];
    const targetZoom = map.getZoom() < 14 ? 14 : map.getZoom();
    map.setView(matchLocation, targetZoom);

    primaryMatchMarker = L.marker(matchLocation, { icon: selectedMatchIcon })
      .addTo(map)
     /*  .bindPopup(`<b>Selected Match</b>`)
      .openPopup(); */
  } else {
    displayFilteredMatches(props.matchesToDisplay);
  }
};

const initMap = () => {
  if (mapContainer.value && !map) {
    map = L.map(mapContainer.value, {
      zoomControl: false,
      attributionControl: false
    });

    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      maxZoom: 19,
      attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(map);

    if (props.latitude != null && props.longitude != null) {
      updateMapForSelectedMatch(props.latitude, props.longitude);
    } else if (props.matchesToDisplay && props.matchesToDisplay.length > 0) {
      displayFilteredMatches(props.matchesToDisplay);
    } else {
      map.setView(defaultCenter, defaultZoom);
    }
  }
};

watch([() => props.latitude, () => props.longitude], ([newLat, newLng]) => {
  updateMapForSelectedMatch(newLat, newLng);
});

watch(() => props.matchesToDisplay, (newMatches, oldMatches) => {
  // Only re-render if the actual list of matches changes, not just the hover state within them.
  if (props.latitude == null && props.longitude == null) { // Only if no specific match is selected
    // A more robust check might be needed if newMatches and oldMatches could be different instances with same content
    if (JSON.stringify(newMatches) !== JSON.stringify(oldMatches)) {
        clearDisplayedMatchMarkers();
        displayFilteredMatches(newMatches);
    }
  }
  updateHoveredMarker(); // Update hover state regardless, as context might have changed
}, { deep: true });

watch(hoveredMatchId, () => {
  updateHoveredMarker();
});

watch(() => props.hoveredMatches, () => {
  updateHoveredMarker();
}, { deep: true });

const updateHoveredMarker = () => {
  if (!map || !displayedMatchMarkers) return;

  // Create a set of hovered match IDs from calendar hover
  const calendarHoveredIds = new Set(props.hoveredMatches?.map(m => m.id) || []);

  displayedMatchMarkers.forEach(marker => {
    const markerOptions = marker.options as CustomMarkerOptions; // Cast to access matchId
    const markerMatchId = markerOptions.matchId;

    // Check if this marker should be highlighted (either from store hover or calendar hover)
    const isHovered = (markerMatchId !== undefined && markerMatchId === hoveredMatchId.value) ||
                     (markerMatchId !== undefined && calendarHoveredIds.has(markerMatchId));

    if (isHovered) {
      marker.setIcon(hoveredMatchIcon);
      marker.setZIndexOffset(1000);
    } else {
      marker.setIcon(displayedMatchIcon);
      marker.setZIndexOffset(0);
    }
  });
};

const _cleanupMap = () => { // Renamed to avoid conflict if this was the issue
  if (map) {
    map.remove();
    map = null;
  }
};

const _handleResize = () => { // Renamed to avoid conflict
  if (map) {
    map.invalidateSize();
  }
};

onMounted(() => {
  initMap();
  window.addEventListener('resize', _handleResize);
});

onUnmounted(() => {
  _cleanupMap();
  window.removeEventListener('resize', _handleResize);
});
</script>

<template>
  <div class="flex flex-col h-full">
    <div ref="mapContainer" class="w-full h-full"></div>
  </div>
</template>

<style scoped>
/* Required CSS adjustments for Leaflet */
:deep(.leaflet-control-container .leaflet-top,
      .leaflet-control-container .leaflet-bottom) {
  z-index: 20; /* Lower than our UI components */
}

:deep(.leaflet-popup-content) {
  margin: 8px;
  font-size: 0.75rem;
}

:deep(.leaflet-popup-content-wrapper) {
  border-radius: 0.5rem;
}
</style>
