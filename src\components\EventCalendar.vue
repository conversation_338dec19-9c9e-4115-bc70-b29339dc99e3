<script setup lang="ts">
import { computed } from 'vue'
import {
  RangeCalendarHeader,
  RangeCalendarHeading,
  RangeCalendarPrevButton,
  RangeCalendarNextButton,
  RangeCalendarGrid,
  RangeCalendarGridHead,
  RangeCalendarGridRow,
  RangeCalendarHeadCell,
  RangeCalendarGridBody,
  RangeCalendarCell,
  RangeCalendarCellTrigger
} from '@/components/ui/range-calendar'
import { RangeCalendarRoot, type DateRange, type DateValue } from 'reka-ui'
import { parseDate } from '@internationalized/date'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import timezone from 'dayjs/plugin/timezone'
import type { Match } from '@/api/feathers-client'

// Configure dayjs plugins
dayjs.extend(utc)
dayjs.extend(timezone)

const props = defineProps<{
  selectedDateRange?: DateRange
  matches?: Match[]
}>()

const emit = defineEmits<{
  (e: 'update:selectedDateRange', value: DateRange | undefined): void
  (e: 'hover:date', matches: Match[]): void
  (e: 'unhover:date'): void
  (e: 'select:match', matchId: number): void
}>()

const handleDateChange = (value: DateRange | undefined) => {
  console.log('handleDateChange from @update:model-value', value)
  if (!value || value.start === undefined) {
    emit('update:selectedDateRange', undefined)
  } else {
    emit('update:selectedDateRange', value)
  }
}

const handleStartValueChange = (startValue: DateValue | undefined) => {
  console.log('handleStartValueChange from @update:start-value', startValue);
  if (startValue === undefined) {
    emit('update:selectedDateRange', undefined);
  }
}

// New handler for double-click event
const handleDoubleClick = () => {
  console.log('Calendar double-clicked, resetting range');
  emit('update:selectedDateRange', undefined);
}

// Group matches by date for displaying dots
const matchesByDate = computed(() => {
  if (!props.matches) return {}

  const grouped: Record<string, Match[]> = {}

  props.matches.forEach(match => {
    if (match.startDate) {
      try {
        // Convert UTC datetime to local date using dayjs
        const localDateString = dayjs.utc(match.startDate).local().format('YYYY-MM-DD')

        // Parse the local date using @internationalized/date for consistency with calendar
        const matchDate = parseDate(localDateString)
        const dateKey = matchDate.toString() // This gives us YYYY-MM-DD format

        // Debug logging to verify date conversion (remove in production)
        console.log(`Match "${match.name}": startDate=${match.startDate} -> localDate=${localDateString} -> dateKey=${dateKey}`)

        if (!grouped[dateKey]) {
          grouped[dateKey] = []
        }
        grouped[dateKey].push(match)
      } catch (error) {
        console.warn(`Failed to parse match date: ${match.startDate}`, error)
      }
    }
  })

  return grouped
})

// Helper function to format month name
const formatMonthName = (dateValue: any) => {
  try {
    // Convert DateValue to a readable month name
    const dateStr = dateValue.toString() // Should be in YYYY-MM-DD format
    const [year, month] = dateStr.split('-')
    const date = new Date(parseInt(year), parseInt(month) - 1, 1)
    return date.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })
  } catch (error) {
    return dateValue.toString()
  }
}

// Helper function to get only the first month name for the header
const getFirstMonthName = (headingValue: string, grid: Array<{ value: DateValue }>) => {
  try {
    if (grid && grid.length > 0) {
      // Use the first month from the grid
      return formatMonthName(grid[0].value)
    }
    // Fallback: if headingValue contains a range, take only the first part
    if (headingValue.includes(' - ')) {
      return headingValue.split(' - ')[0]
    }
    return headingValue
  } catch {
    return headingValue
  }
}

// Helper function to get matches for a specific date
const getMatchesForDate = (date: DateValue): Match[] => {
  if (!date) return []

  try {
    // Convert DateValue to string format that matches our grouped dates
    // DateValue from reka-ui should have toString() that gives YYYY-MM-DD
    const dateStr = date.toString()

    return matchesByDate.value[dateStr] || []
  } catch (error) {
    console.warn('Error converting date:', date, error)
    return []
  }
}

// Hover event handlers
const handleDateHover = (date: DateValue) => {
  const matches = getMatchesForDate(date)
  if (matches.length > 0) {
    emit('hover:date', matches)
  }
}

const handleDateUnhover = () => {
  emit('unhover:date')
}

// Handle match selection from tooltip
const handleSelectMatch = (matchId: number) => {
  emit('select:match', matchId)
}

import {
  SidebarGroup,
  SidebarGroupContent,
} from '@/components/ui/sidebar'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import { ChevronRight } from 'lucide-vue-next'
</script>

<template>
  <TooltipProvider>
    <SidebarGroup class="px-0">
      <SidebarGroupContent>
      <RangeCalendarRoot
        v-slot="{ grid, weekDays }"
        data-slot="range-calendar"
        class="p-3"
        :week-starts-on="1"
        :number-of-months="2"
        :model-value="props.selectedDateRange"
        @update:model-value="handleDateChange"
        @update:start-value="handleStartValueChange"
        @dblclick="handleDoubleClick"
      >
        <RangeCalendarHeader>
          <RangeCalendarHeading>
            <template #default="{ headingValue }">
              {{ getFirstMonthName(headingValue, grid) }}
            </template>
          </RangeCalendarHeading>
          <div class="flex items-center gap-1">
            <RangeCalendarPrevButton />
            <RangeCalendarNextButton />
          </div>
        </RangeCalendarHeader>

        <div class="flex flex-col gap-y-4 mt-4">
          <div v-for="(month, monthIndex) in grid" :key="month.value.toString()">
            <!-- Month name header (show for all months except the first one) -->
            <div v-if="monthIndex > 0" class="text-sm font-medium text-center mb-2 text-muted-foreground">
              {{ formatMonthName(month.value) }}
            </div>

            <RangeCalendarGrid>
              <RangeCalendarGridHead>
              <RangeCalendarGridRow>
                <RangeCalendarHeadCell
                  v-for="day in weekDays"
                  :key="day"
                  class="w-[14%]"
                >
                  {{ day }}
                </RangeCalendarHeadCell>
              </RangeCalendarGridRow>
            </RangeCalendarGridHead>
            <RangeCalendarGridBody>
              <RangeCalendarGridRow v-for="(weekDates, index) in month.rows" :key="`weekDate-${index}`" class="mt-2 w-full">
                <RangeCalendarCell
                  v-for="weekDate in weekDates"
                  :key="weekDate.toString()"
                  :date="weekDate"
                  class="w-[14%] [&:has([data-selected])]:bg-sidebar-primary [&:has([data-selected])]:text-sidebar-primary-foreground"
                >
                  <Tooltip v-if="getMatchesForDate(weekDate).length > 0" :delay-duration="300">
                    <TooltipTrigger as-child>
                      <RangeCalendarCellTrigger
                        :day="weekDate"
                        :month="month.value"
                        class="!flex !flex-col !items-center !justify-center !h-8 !w-full !text-xs !gap-0"
                        @mouseenter="handleDateHover(weekDate)"
                        @mouseleave="handleDateUnhover"
                      >
                    <div class="flex-1 flex items-center justify-center leading-none">
                      {{ weekDate.day }}
                    </div>
                    <div class="flex gap-0.5 justify-center min-h-[3px]">
                      <div
                        v-for="dotIndex in Math.min(getMatchesForDate(weekDate).length, 3)"
                        :key="dotIndex"
                        class="w-1 h-1 bg-primary rounded-full"
                      ></div>
                      <div
                        v-if="getMatchesForDate(weekDate).length > 3"
                        class="w-1 h-1 bg-primary rounded-full opacity-60"
                      ></div>
                    </div>
                      </RangeCalendarCellTrigger>
                    </TooltipTrigger>
                    <TooltipContent side="top" class="p-2 max-w-xs">
                      <div class="space-y-1">
                        <div v-for="match in getMatchesForDate(weekDate)" :key="match.id" class="flex items-center justify-between gap-2">
                          <span class="text-sm truncate">{{ match.name }}</span>
                          <button
                            @click="handleSelectMatch(match.id)"
                            class="p-1 hover:bg-muted rounded-sm flex-shrink-0 text-muted-foreground hover:text-foreground transition-colors"
                            :title="`Select ${match.name}`"
                          >
                            <ChevronRight class="h-3 w-3" />
                          </button>
                        </div>
                      </div>
                    </TooltipContent>
                  </Tooltip>
                  <RangeCalendarCellTrigger
                    v-else
                    :day="weekDate"
                    :month="month.value"
                    class="!flex !flex-col !items-center !justify-center !h-8 !w-full !text-xs !gap-0"
                    @mouseenter="handleDateHover(weekDate)"
                    @mouseleave="handleDateUnhover"
                  >
                    <div class="flex-1 flex items-center justify-center leading-none">
                      {{ weekDate.day }}
                    </div>
                    <div class="flex gap-0.5 justify-center min-h-[3px]">
                      <div
                        v-for="dotIndex in Math.min(getMatchesForDate(weekDate).length, 3)"
                        :key="dotIndex"
                        class="w-1 h-1 bg-primary rounded-full"
                      ></div>
                      <div
                        v-if="getMatchesForDate(weekDate).length > 3"
                        class="w-1 h-1 bg-primary rounded-full opacity-60"
                      ></div>
                    </div>
                  </RangeCalendarCellTrigger>
                </RangeCalendarCell>
              </RangeCalendarGridRow>
            </RangeCalendarGridBody>
            </RangeCalendarGrid>
          </div>
        </div>
      </RangeCalendarRoot>
      </SidebarGroupContent>
    </SidebarGroup>
  </TooltipProvider>
</template>
