<script setup lang="ts">
import type { SidebarProps } from '@/components/ui/sidebar'

import NavMain from '../NavMain.vue'
import NavUser from '../NavUser.vue'
import GearSwitcher from '../GearSwitcher.vue'
import SidebarFilters from '../SidebarFilters.vue'
import IconBowAndArrow from '../icons/IconBowAndArrow.vue'
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
  SidebarSeparator,
} from '@/components/ui/sidebar'

import {
  Target,
  Heart,
  Search,
  BarChart2,
  Mail,
  ClipboardCheck,
  Eye,
  Award,
  Archive
} from 'lucide-vue-next'

const props = withDefaults(defineProps<SidebarProps>(), {
  collapsible: 'icon',
  variant: 'sidebar',
})

// Split menu items into two groups
const matchesItems = [
  {
    title: 'Search',
    url: '/',
    icon: Search,
    isActive: true,
  },
  {
    title: 'My Signups',
    url: '/matches/signups',
    icon: ClipboardCheck,
  },
  {
    title: 'Watched',
    url: '/matches/watched',
    icon: Eye,
  },
  {
    title: 'Results',
    url: '/matches/results',
    icon: Award,
  },
  {
    title: 'Archive',
    url: '/matches/archive',
    icon: Archive,
  },
]

const otherItems = [
  {
    title: 'Statistics',
    url: '/statistics',
    icon: BarChart2,
  },
  {
    title: 'Favourites',
    url: '/favourites',
    icon: Heart,
  },
  {
    title: 'Messages',
    url: '/messages',
    icon: Mail,
  },
]

// This is sample data.
const data = {
  user: {
    name: 'Kowalewski Kazimierz',
    email: '<EMAIL>',
    avatar: '/avatar.jpg',
  },
  gears: [
    {
      name: 'Matthews RX36',
      logo: IconBowAndArrow,
      plan: 'AMFS',
    },
    {
      name: 'Hoyt Invicta',
      logo: IconBowAndArrow,
      plan: 'AMFU',
    },
    {
      name: 'PSE Perform-X',
      logo: IconBowAndArrow,
      plan: 'AMBH',
    },
  ],
  matchesItems,
  otherItems,
  filters: [
    {
      name: 'Federations',
      items: ['ALL', 'IFAA', 'WA', 'HDH', 'SLA 3D', 'AP']
    },

  ]
}
</script>

<template>
  <Sidebar v-bind="props">
    <SidebarHeader>
      <GearSwitcher :gears="data.gears" />
    </SidebarHeader>
    <SidebarContent>
      <NavMain :matchesItems="data.matchesItems" :otherItems="data.otherItems" />
      <SidebarSeparator class="mx-0" />
      <SidebarFilters :calendars="data.filters" />
      <SidebarSeparator class="mx-0" />
    </SidebarContent>
    <SidebarFooter>
      <NavUser :user="data.user" />
    </SidebarFooter>
    <SidebarRail />
  </Sidebar>
</template>
