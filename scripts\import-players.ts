import fs from 'fs';
import path from 'path';

import { parse } from 'csv-parse';

import { app } from '../src/app';
import type { Player, PlayerData } from '../src/services/players/players.schema';
import type { User, UserData } from '../src/services/users/users.schema';

// Define an interface for Feathers error type
interface FeathersError extends Error {
  code: number;
  className: string;
  data?: any[];
}

/**
 * Convert string to boolean
 */
const parseBool = (value: string | undefined): boolean | undefined => {
  if (value === undefined || value === '' || value === '\\N') return undefined;
  if (value === '0') return false;
  if (value === '1') return true;
  return undefined;
};

/**
 * Parse JSON field from string 
 * Returns the original JSON string if valid, undefined if invalid/empty
 */
const parseJsonField = (value: string | undefined): string | undefined => {
  if (!value || value === '\\N' || value === '-1') return undefined;

  try {
    // Just validate that it's valid JSON, but return the original string
    JSON.parse(value);
    return value;
  } catch (error) {
    console.error(`Error parsing JSON value: ${value}`, error);
    return undefined;
  }
};

/**
 * Parse date fields from DD.MM.YYYY to YYYY-MM-DD
 */
const parseDate = (value: string | undefined): string | undefined => {
  if (!value || value === '\\N') return undefined;

  // Handle date format DD.MM.YYYY
  if (value.includes('.')) {
    const [day, month, year] = value.split('.');
    return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
  }

  return value;
};

/**
 * Parse timestamp fields from DD.MM.YYYY HH:MM to ISO 8601 format (YYYY-MM-DDTHH:MM:SS.sssZ)
 */
const parseTimestamp = (value: string | undefined): string | undefined => {
  if (!value || value === '\\N') return undefined;

  console.log('parseTimestamp value:', value);

  // Handle date-time format DD.MM.YYYY HH:MM
  if (value.includes(' ') && value.includes('.')) {
    const [datePart, timePart] = value.split(' ');
    const dateParts = datePart.split('.');

    if (dateParts.length !== 3) {
      console.error(`Invalid date format: ${value}`);
      return undefined;
    }

    const [day, month, year] = dateParts;

    // Validate date parts
    if (!day || !month || !year || day.length === 0 || month.length === 0 || year.length === 0) {
      console.error(`Invalid date parts: ${value}`);
      return undefined;
    }

    // Format time part - add seconds if missing
    let formattedTime = timePart || '00:00:00';
    if (!formattedTime.includes(':')) formattedTime += ':00:00';
    if (formattedTime.split(':').length === 2) formattedTime += ':00';

    // Validate time format
    const timeParts = formattedTime.split(':');
    if (timeParts.length !== 3) {
      console.error(`Invalid time format: ${value}`);
      return undefined;
    }

    // Create ISO 8601 date-time string
    const isoString = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}T${formattedTime}.000Z`;

    // Validate the resulting date
    const date = new Date(isoString);
    if (isNaN(date.getTime())) {
      console.error(`Invalid date created: ${isoString} from ${value}`);
      return undefined;
    }

    console.log(`Parsed timestamp: ${value} -> ${isoString}`);
    return isoString;
  }

  // Try to parse as existing ISO date
  const date = new Date(value);
  if (!isNaN(date.getTime())) {
    return date.toISOString();
  }

  console.error(`Could not parse timestamp: ${value}`);
  return undefined;
};

/**
 * Parse string fields, handling special null values
 */
const parseString = (value: string | undefined): string | undefined => {
  if (!value || value === '\\N' || value === 'NULL' || value === 'null' || value === '') {
    return undefined;
  }
  return value.trim();
};

/**
 * Parse number fields, handling special values and ensuring type safety
 */
const parseNumber = (value: string | undefined): number | undefined => {
  if (!value || value === '\\N' || value === '-1' || value === '') return undefined;

  // Trim whitespace and handle common null values
  const trimmedValue = value.toString().trim();
  if (trimmedValue === '' || trimmedValue === '\\N' || trimmedValue === 'NULL' || trimmedValue === 'null') {
    return undefined;
  }

  const num = Number(trimmedValue);
  if (isNaN(num)) {
    console.warn(`Failed to parse number: "${value}" -> "${trimmedValue}"`);
    return undefined;
  }

  return num;
};

/**
 * Parse decimal fields (for latitude/longitude), handling comma as decimal separator
 */
const parseDecimal = (value: string | undefined): number | undefined => {
  if (!value || value === '\\N' || value === '') return undefined;

  // Replace comma with dot for decimal separator
  const normalizedValue = value.replace(',', '.');
  const num = parseFloat(normalizedValue);
  return !isNaN(num) ? num : undefined;
};

/**
 * Generate a random password for legacy users
 */
const generateRandomPassword = (): string => {
  return Math.random().toString(36).slice(-12) + Math.random().toString(36).slice(-12);
};

/**
 * Main import function for players from CSV
 */
async function importPlayers(csvFilePath: string) {
  const players = app.service('players');
  const users = app.service('users');

  // Ensure the path is absolute
  const filePath = path.isAbsolute(csvFilePath)
    ? csvFilePath
    : path.join(process.cwd(), csvFilePath);

  console.log(`Importing players from ${filePath}`);

  const parser = fs
    .createReadStream(filePath)
    .pipe(parse({
      delimiter: ';', // Semicolon-separated as per your file format
      columns: false, // Don't use headers, we'll map by position
      skip_empty_lines: true,
      relax_quotes: true,
      skip_records_with_empty_values: false // We want to handle empty values manually
    }));

  let recordCount = 0;
  let importCount = 0;
  let errorCount = 0;
  const createdUserIds: number[] = [];
  const errorLogPath = path.join(process.cwd(), 'import-players-errors.txt');
  // Clear previous error log
  fs.writeFileSync(errorLogPath, '');

  for await (const record of parser) {
    recordCount++;

    // Skip completely empty rows
    if (!Array.isArray(record) || record.every(v => !v || v === '')) {
      continue;
    }

    try {
      // CSV columns mapping based on the example data (semicolon-separated):
      // 0: id (legacy) -> Player.legacyId
      // 1: is_active -> User.isActive and Player.isActive  
      // 2: email -> User.email
      // 3: password (hashed) -> ignored, generate new random password
      // 4: address -> Player.address
      // 5: postcode -> Player.zipcode
      // 6: city -> Player.city
      // 7: country -> Player.country
      // 8: phone -> Player.phone
      // 9: birthdate (DD.MM.YYYY) -> Player.birthdate
      // 10: sex (M/F) -> Player.sex
      // 11: firstname -> Player.firstname
      // 12: lastname -> Player.lastname
      // 13: latitude -> Player.latitude
      // 14: longitude -> Player.longitude
      // 15: ??? (seems to be \N)
      // 16: activated_at (DD.MM.YYYY HH:MM) -> User.activatedAt and Player.activatedAt
      // 17: ??? (seems to be token or similar)
      // 18: avatar -> User.avatar and Player.avatar
      // 19: is_active again?
      // 20: ??? (seems to be \N)  
      // 21: max_distance?
      // 22: equipment_category (JSON array) -> Player.equipmentCategory
      // 23: license_required?
      // 24: ??? (\N)
      // 25: ??? (\N)
      // 26: language
      // 27: created_at (DD.MM.YYYY HH:MM) -> Player.createdAt
      // 28: updated_at (DD.MM.YYYY HH:MM) -> Player.updatedAt
      // 29: deleted_at (\N) -> Player.deletedAt
      // 30: deleted_by (\N) -> Player.deletedBy

      const [
        legacyId, isActiveStr, email, hashedPassword, address, postcode, city, country, phone,
        birthdateStr, sex, firstname, lastname, latitudeStr, longitudeStr, field15,
        activatedAtStr, field17, avatar, isActiveStr2, field20, maxDistance, equipmentCategoryStr,
        licenseRequired, field24, field25, language, createdAtStr, updatedAtStr, deletedAtStr, deletedByStr
      ] = record;

      // Debug logging for legacyId parsing
      console.log(`Record #${recordCount} - Raw legacyId: "${legacyId}" (type: ${typeof legacyId})`);
      const parsedLegacyId = parseNumber(legacyId);
      console.log(`Record #${recordCount} - Parsed legacyId: ${parsedLegacyId}`);

      // Skip records without essential data
      if (!parseString(email)) {
        console.log(`Skipping record #${recordCount} - no email`);
        continue;
      }

      // Skip records that were never activated (activated_at = \N)
      if (!activatedAtStr || activatedAtStr === '\\N') {
        console.log(`Skipping record #${recordCount} - user never activated (${email})`);
        continue;
      }

      // Skip records where user is inactive (is_active = 0)
      if (isActiveStr === '0') {
        console.log(`Skipping record #${recordCount} - user inactive (${email})`);
        continue;
      }

      // Create user first
      const userData: UserData = {
        email: email,
        password: generateRandomPassword(), // Generate new password since we can't use the hashed one
        isActive: parseBool(isActiveStr) ?? true, // Map CSV is_active to User.isActive
        ...(parseString(language) ? { language: parseString(language) } : {})
      };

      // Add optional user fields
      const parsedAvatar = parseString(avatar);
      if (parsedAvatar && parsedAvatar !== 'woman_icon_2.jpg' && parsedAvatar !== 'man_icon_2.jpg') {
        userData.avatar = parsedAvatar;
      }

      // Map CSV activated_at to User.activatedAt
      if (activatedAtStr && activatedAtStr !== '\\N') {
        const parsedActivatedAt = parseTimestamp(activatedAtStr);
        if (parsedActivatedAt) {
          userData.activatedAt = parsedActivatedAt;
        } else {
          console.warn(`Skipping invalid activatedAt for user ${email}: ${activatedAtStr}`);
        }
      }

      console.log(`Creating user for: ${email}`);
      const createdUser = await users.create(userData);
      createdUserIds.push(createdUser.id);

      // Create player data
      const playerData: PlayerData = {
        legacyId: parsedLegacyId,
        userId: createdUser.id,
        isActive: parseBool(isActiveStr) ?? true, // Map CSV is_active to Player.isActive
      };

      // Add optional player fields
      if (parseString(address)) playerData.address = parseString(address);
      if (parseString(postcode)) playerData.zipcode = parseString(postcode);
      if (parseString(city)) playerData.city = parseString(city);
      if (parseString(country)) playerData.country = parseString(country);
      if (parseString(phone)) playerData.phone = parseString(phone);
      if (birthdateStr && birthdateStr !== '\\N') playerData.birthdate = parseDate(birthdateStr);

      // Keep sex field as M/F from CSV
      if (parseString(sex)) {
        playerData.sex = parseString(sex);
      }

      if (parseString(firstname)) playerData.firstname = parseString(firstname);
      if (parseString(lastname)) playerData.lastname = parseString(lastname);

      // Parse coordinates
      if (latitudeStr && latitudeStr !== '\\N') playerData.latitude = parseDecimal(latitudeStr);
      if (longitudeStr && longitudeStr !== '\\N') playerData.longitude = parseDecimal(longitudeStr);

      // Handle equipment category
      if (equipmentCategoryStr && equipmentCategoryStr !== '\\N') {
        playerData.equipmentCategory = parseJsonField(equipmentCategoryStr);
      }

      // Handle avatar
      if (parsedAvatar && parsedAvatar !== 'woman_icon_2.jpg' && parsedAvatar !== 'man_icon_2.jpg') {
        playerData.avatar = parsedAvatar;
      }

      // Handle activated at - Map CSV activated_at to Player.activatedAt
      if (activatedAtStr && activatedAtStr !== '\\N') {
        const parsedActivatedAt = parseTimestamp(activatedAtStr);
        if (parsedActivatedAt) {
          playerData.activatedAt = parsedActivatedAt;
        } else {
          console.warn(`Skipping invalid activatedAt for player ${email}: ${activatedAtStr}`);
        }
      }

      // Create additional data for patching timestamps if needed
      const additionalData: Partial<Player> = {};

      if (createdAtStr && createdAtStr !== '\\N') {
        const parsedCreatedAt = parseTimestamp(createdAtStr);
        if (parsedCreatedAt) {
          additionalData.createdAt = parsedCreatedAt;
        } else {
          console.warn(`Skipping invalid createdAt for player ${email}: ${createdAtStr}`);
        }
      }

      if (updatedAtStr && updatedAtStr !== '\\N') {
        const parsedUpdatedAt = parseTimestamp(updatedAtStr);
        if (parsedUpdatedAt) {
          additionalData.updatedAt = parsedUpdatedAt;
        } else {
          console.warn(`Skipping invalid updatedAt for player ${email}: ${updatedAtStr}`);
        }
      }

      if (deletedAtStr && deletedAtStr !== '\\N') {
        const parsedDeletedAt = parseTimestamp(deletedAtStr);
        if (parsedDeletedAt) {
          additionalData.deletedAt = parsedDeletedAt;
        } else {
          console.warn(`Skipping invalid deletedAt for player ${email}: ${deletedAtStr}`);
        }
      }

      if (deletedByStr && deletedByStr !== '\\N') {
        additionalData.deletedBy = parseNumber(deletedByStr);
      }

      // Remove any undefined values to avoid schema validation issues
      Object.keys(additionalData).forEach(key => {
        if (additionalData[key as keyof Player] === undefined) {
          delete additionalData[key as keyof Player];
        }
      });

      // Create the player
      const createdPlayer = await players.create(playerData);

      // Apply additional timestamp data if available
      if (Object.keys(additionalData).length > 0) {
        await players.patch(createdPlayer.id, additionalData);
      }

      importCount++;
      console.log(`Imported player #${importCount}: ${playerData.firstname} ${playerData.lastname} (${email}) - Legacy ID: ${parsedLegacyId}`);

    } catch (error: unknown) {
      errorCount++;
      console.error(`Error importing record #${recordCount}:`, error);
      // Log error to file
      const errorMsg = [
        `Error importing record #${recordCount}:`,
        JSON.stringify(error, null, 2),
        'Record data:',
        JSON.stringify(record, null, 2),
        '\n'
      ].join('\n');
      fs.appendFileSync(errorLogPath, errorMsg);
      // Enhanced error logging with proper type checking
      if (error && typeof error === 'object' && 'data' in error) {
        const feathersError = error as FeathersError;
        if (Array.isArray(feathersError.data) && feathersError.data.length > 0) {
          const details = [
            'Validation error details:',
            JSON.stringify(feathersError.data, null, 2),
            'Problematic record data:',
            JSON.stringify({
              email: Object.values(record)[2],
              firstname: Object.values(record)[11],
              lastname: Object.values(record)[12],
              legacyId: Object.values(record)[0]
            }, null, 2),
            '\n'
          ].join('\n');
          fs.appendFileSync(errorLogPath, details);
          console.error('Validation error details:', JSON.stringify(feathersError.data, null, 2));
          console.error('Problematic record data:', JSON.stringify({
            email: Object.values(record)[2],
            firstname: Object.values(record)[11],
            lastname: Object.values(record)[12],
            legacyId: Object.values(record)[0]
          }, null, 2));
        }
      }
    }
  }

  console.log(`Import complete: ${importCount} players imported successfully, ${errorCount} errors`);
  console.log(`Created ${createdUserIds.length} new users`);

  // Note: Users created during import will have random passwords
  console.log('Note: All imported users have been assigned random passwords and will need to reset them');
}

// If this script is run directly (not imported)
if (require.main === module) {
  const csvFilePath = process.argv[2];
  if (!csvFilePath) {
    console.error('Error: No CSV file path provided');
    console.log('Usage: pnpm exec ts-node scripts/import-players.ts <path/to/csv/file>');
    process.exit(1);
  }

  // Run the import
  importPlayers(csvFilePath)
    .then(() => process.exit(0))
    .catch(error => {
      console.error('Import failed:', error);
      process.exit(1);
    });
}

export { importPlayers };
