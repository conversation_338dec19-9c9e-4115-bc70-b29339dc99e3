<script setup lang="ts">
import { computed } from 'vue' // Added
import { useRouter } from 'vue-router' // Added
import { useAuthStore, type User as AuthUser } from '@/stores/auth' // Added
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from '@/components/ui/avatar'

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from '@/components/ui/sidebar'
import {
  // BadgeCheck, // Not used
  Bell,
  ChevronsUpDown,
  // CreditCard, // Not used
  LogOut,
  // Sparkles, // Not used
  User,
  Settings,
} from 'lucide-vue-next'

// const props = defineProps<{ // Props are no longer needed as we use the store
//   user: {
//     name: string
//     email: string
//     avatar: string
//   }
// }>()

const authStore = useAuthStore()
const router = useRouter()
const { isMobile } = useSidebar()

const currentUser = computed(() => authStore.user as AuthUser | null) // Use user from store

const userInitials = computed(() => {
  if (currentUser.value) {
    if (currentUser.value.email) {
      return currentUser.value.email.substring(0, 2).toUpperCase()
    }
    // Ensure name is a string before calling string methods
    if (typeof currentUser.value.name === 'string' && currentUser.value.name) {
      const parts = currentUser.value.name.split(' ')
      if (parts.length > 1 && parts[0] && parts[1]) {
        return parts[0][0].toUpperCase() + parts[1][0].toUpperCase()
      }
      return currentUser.value.name.substring(0, 2).toUpperCase()
    }
  }
  return 'AP' // Default fallback
})

const userDisplayName = computed(() => {
  return currentUser.value?.name || currentUser.value?.email || 'User'
})

const userDisplayEmail = computed(() => {
  return currentUser.value?.email || ''
})

// TODO: Add a real avatar URL to user object in auth store or use a default
const userAvatarSrc = computed(() => {
  // Ensure avatar is a string, provide a fallback if not or undefined
  return typeof currentUser.value?.avatar === 'string' ? currentUser.value.avatar : ''
})

async function handleLogout() {
  await authStore.logout()
  router.push({ name: 'login' }) // Explicitly redirect to login
}
</script>

<template>
  <SidebarMenu v-if="authStore.isAuthenticated && currentUser">
    <SidebarMenuItem>
      <DropdownMenu>
        <DropdownMenuTrigger as-child>
          <SidebarMenuButton
            size="lg"
            class="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
          >
            <Avatar class="h-8 w-8 rounded-lg">
              <AvatarImage :src="userAvatarSrc" :alt="userDisplayName" />
              <AvatarFallback class="rounded-lg">
                {{ userInitials }}
              </AvatarFallback>
            </Avatar>
            <div class="grid flex-1 text-left text-sm leading-tight">
              <span class="truncate font-medium">{{ userDisplayName }}</span>
              <span v-if="userDisplayEmail" class="truncate text-xs flex items-center gap-1">
                <!-- <img src="/flags/pl.svg" class="h-3 inline-block" /> Placeholder, remove or make dynamic -->
                {{ userDisplayEmail }}
              </span>
            </div>
            <ChevronsUpDown class="ml-auto size-4" />
          </SidebarMenuButton>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          class="w-[--reka-dropdown-menu-trigger-width] min-w-56 rounded-lg"
          :side="isMobile ? 'bottom' : 'bottom'"
          align="end"
          :side-offset="4"
        >
          <DropdownMenuLabel class="p-0 font-normal">
            <div class="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
              <Avatar class="h-8 w-8 rounded-lg">
                <AvatarImage :src="userAvatarSrc" :alt="userDisplayName" />
                <AvatarFallback class="rounded-lg">
                  {{ userInitials }}
                </AvatarFallback>
              </Avatar>
              <div class="grid flex-1 text-left text-sm leading-tight">
                <span class="truncate font-semibold">{{ userDisplayName }}</span>
                <span v-if="userDisplayEmail" class="truncate text-xs flex items-center gap-1">
                  <!-- <img src="/flags/pl.svg" class="h-3 inline-block" /> Placeholder, remove or make dynamic -->
                  {{ userDisplayEmail }}
                </span>
              </div>
            </div>
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuGroup>
            <DropdownMenuItem @click="router.push('/account')"> <!-- TODO: Create /account route -->
              <User class="mr-2 h-4 w-4" /> <!-- Added class for spacing -->
              Account
            </DropdownMenuItem>
            <DropdownMenuItem @click="router.push('/settings')"> <!-- TODO: Create /settings route -->
              <Settings class="mr-2 h-4 w-4" /> <!-- Added class for spacing -->
              Settings
            </DropdownMenuItem>
            <DropdownMenuItem @click="router.push('/notifications')"> <!-- TODO: Create /notifications route -->
              <Bell class="mr-2 h-4 w-4" /> <!-- Added class for spacing -->
              Notifications
            </DropdownMenuItem>
          </DropdownMenuGroup>
          <DropdownMenuSeparator />
          <DropdownMenuItem @click="handleLogout">
            <LogOut class="mr-2 h-4 w-4" /> <!-- Added class for spacing -->
            Log out
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </SidebarMenuItem>
  </SidebarMenu>
  <SidebarMenu v-else>
    <SidebarMenuItem>
       <SidebarMenuButton
        size="lg"
        @click="router.push({ name: 'login' })"
      >
        <LogOut class="mr-2 h-4 w-4 rotate-180" /> <!-- Icon indicating login -->
        Sign In
      </SidebarMenuButton>
    </SidebarMenuItem>
  </SidebarMenu>
</template>
